VERSION ""
NS_ : 
    NS_DESC_
    CM_
    BA_DEF_
    BA_
    VAL_
    CAT_DEF_
    CAT_
    FILTER
    BA_DEF_DEF_
    EV_DATA_
    ENVVAR_DATA_
    SGTYPE_
    SGTYPE_VAL_
    BA_DEF_SGTYPE_
    BA_SGTYPE_
    SIG_TYPE_REF_
    VAL_TABLE_
    SIG_GROUP_
    SIG_VALTYPE_
    SIGTYPE_VALTYPE_
    BO_TX_BU_
    BA_DEF_REL_
    BA_REL_
    BA_DEF_DEF_REL_
    BU_SG_REL_
    BU_EV_REL_
    BU_BO_REL_
    SG_MUL_VAL_

BS_:
BU_: ECU

BO_ 101 TotalVehicleHours: 4 ECU
 SG_ TotalVehicleHours : 0|32@1+ (0.05,0) [0|4294967295] "h"  ECU
CM_ BO_ 101 "Total Vehicle Hours";
CM_ SG_ 101 TotalVehicleHours "Total hours the vehicle has been running";

BO_ 180 BarometricPressure: 1 ECU
 SG_ BarometricPressure : 0|8@1+ (2,0) [0|255] "kPa"  ECU
CM_ BO_ 180 "Barometric Pressure";
CM_ SG_ 180 BarometricPressure "Barometric pressure in kPa";

BO_ 182 Temperature: 2 ECU
 SG_ Temperature : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 182 "Temperature";
CM_ SG_ 182 Temperature "Temperature in deg C";

BO_ 209 Engine1Speed: 2 ECU
 SG_ Engine1Speed : 0|16@1+ (0.125,0) [0|65535] "rpm"  ECU
CM_ BO_ 209 "Engine 1 Speed";
CM_ SG_ 209 Engine1Speed "Engine 1 speed in rpm";

BO_ 211 Temperature1: 2 ECU
 SG_ Temperature1 : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 211 "Temperature 1";
CM_ SG_ 211 Temperature1 "Temperature 1 in deg C";

BO_ 213 Engine1OilPressure: 1 ECU
 SG_ Engine1OilPressure : 0|8@1+ (4,0) [0|255] "kPa"  ECU
CM_ BO_ 213 "Engine 1 Oil Pressure";
CM_ SG_ 213 Engine1OilPressure "Engine 1 oil pressure in kPa";

BO_ 222 Temperature: 1 ECU
 SG_ Temperature : 0|8@1+ (1,-40) [0|255] "deg C"  ECU
CM_ BO_ 222 "Temperature";
CM_ SG_ 222 Temperature "Temperature in deg C";

BO_ 229 Engine1FuelLevel: 1 ECU
 SG_ Engine1FuelLevel : 0|8@1+ (10,0) [0|255] "%"  ECU
CM_ BO_ 229 "Engine 1 Fuel Level";
CM_ SG_ 229 Engine1FuelLevel "Engine 1 fuel level in percent";

BO_ 231 Engine1FuelRate: 2 ECU
 SG_ Engine1FuelRate : 0|16@1+ (0.05,0) [0|65535] "L/h"  ECU
CM_ BO_ 231 "Engine 1 Fuel Rate";
CM_ SG_ 231 Engine1FuelRate "Engine 1 fuel rate in L/h";

BO_ 266 TemperatureRight: 2 ECU
 SG_ TemperatureRight : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 266 "Temperature - Right";
CM_ SG_ 266 TemperatureRight "Right side temperature in deg C";

BO_ 267 TemperatureLeft: 2 ECU
 SG_ TemperatureLeft : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 267 "Temperature - Left";
CM_ SG_ 267 TemperatureLeft "Left side temperature in deg C";

BO_ 675 Temperature1: 1 ECU
 SG_ Temperature1 : 0|8@1+ (1,-40) [0|255] "deg C"  ECU
CM_ BO_ 675 "Temperature 1";
CM_ SG_ 675 Temperature1 "Temperature 1 in deg C";

BO_ 1001 Front1MainLoader: 2 ECU
 SG_ Front1MainLoader : 0|16@1+ (0.003906,0) [0|65535] "MPa"  ECU
CM_ BO_ 1001 "1/Front 1/Main/Loader";
CM_ SG_ 1001 Front1MainLoader "Front 1/Main/Loader pressure in MPa";

BO_ 1201 Rear2: 2 ECU
 SG_ Rear2 : 0|16@1+ (0.003906,0) [0|65535] "MPa"  ECU
CM_ BO_ 1201 "2/Rear 1";
CM_ SG_ 1201 Rear2 "Rear 1 pressure in MPa";

BO_ 1401 Front2: 2 ECU
 SG_ Front2 : 0|16@1+ (0.003906,0) [0|65535] "MPa"  ECU
CM_ BO_ 1401 "3/Front 2";
CM_ SG_ 1401 Front2 "Front 2 pressure in MPa";

BO_ 1601 Rear4: 2 ECU
 SG_ Rear4 : 0|16@1+ (0.003906,0) [0|65535] "MPa"  ECU
CM_ BO_ 1601 "4/Rear 2";
CM_ SG_ 1601 Rear4 "Rear 2 pressure in MPa";

BO_ 4001 StickArmState: 1 ECU
 SG_ StickArmState : 0|8@1+ (1,0) [0|3] ""  ECU
CM_ BO_ 4001 "Stick / Arm State";
CM_ SG_ 4001 StickArmState "Stick/Arm state";
VAL_ 4001 StickArmState 3 "Stick in" 2 "Stick stop" 1 "Stick out" 0 "Abnormal";

BO_ 4002 BoomState: 1 ECU
 SG_ BoomState : 0|8@1+ (1,0) [0|3] ""  ECU
CM_ BO_ 4002 "Boom State";
CM_ SG_ 4002 BoomState "Boom state";
VAL_ 4002 BoomState 3 "Boom up" 2 "Boom stop" 1 "Boom down" 0 "Abnormal";

BO_ 4003 BucketState: 1 ECU
 SG_ BucketState : 0|8@1+ (1,0) [0|3] ""  ECU
CM_ BO_ 4003 "Bucket State";
CM_ SG_ 4003 BucketState "Bucket state";
VAL_ 4003 BucketState 3 "Bucket dump" 2 "Bucket stop" 1 "Bucket fill" 0 "Abnormal";

BO_ 4004 ClamState: 1 ECU
 SG_ ClamState : 0|8@1+ (1,0) [0|3] ""  ECU
CM_ BO_ 4004 "Clam State";
CM_ SG_ 4004 ClamState "Clam state";
VAL_ 4004 ClamState 3 "Clam close" 2 "Clam stop" 1 "Clam open" 0 "Abnormal";

BO_ 4007 SwingState: 1 ECU
 SG_ SwingState : 0|8@1+ (1,0) [0|3] ""  ECU
CM_ BO_ 4007 "Swing State";
CM_ SG_ 4007 SwingState "Swing state";
VAL_ 4007 SwingState 3 "Slew right" 2 "Slew stop" 1 "Slew left" 0 "Abnormal";

BO_ 1155 Temperature: 1 ECU
 SG_ Temperature : 0|8@1+ (1,-40) [0|255] "deg C"  ECU
CM_ BO_ 1155 "Temperature";
CM_ SG_ 1155 Temperature "Temperature in deg C";

BO_ 121 SecondsSince: 4 ECU
 SG_ SecondsSince : 0|32@1+ (1,0) [0|4294967295] "sec"  ECU
CM_ BO_ 121 "Seconds since";
CM_ SG_ 121 SecondsSince "Seconds since event";

BO_ 187 Longitude: 4 ECU
 SG_ Longitude : 0|32@1+ (1e-7,0) [0|4294967295] "deg"  ECU
CM_ BO_ 187 "Longitude";
CM_ SG_ 187 Longitude "Longitude in degrees";

BO_ 186 Latitude: 4 ECU
 SG_ Latitude : 0|32@1+ (1e-7,0) [0|4294967295] "deg"  ECU
CM_ BO_ 186 "Latitude";
CM_ SG_ 186 Latitude "Latitude in degrees";

BO_ 185 Altitude: 2 ECU
 SG_ Altitude : 0|16@1+ (1,-2500) [0|65535] "m"  ECU
CM_ BO_ 185 "Altitude";
CM_ SG_ 185 Altitude "Altitude in meters";

BO_ 150 VehicleSpeed: 2 ECU
 SG_ VehicleSpeed : 0|16@1+ (0.003906,0) [0|65535] "km/h"  ECU
CM_ BO_ 150 "Vehicle Speed";
CM_ SG_ 150 VehicleSpeed "Vehicle speed in km/h";

BO_ 189 NavigationFault: 4 ECU
 SG_ NavigationFault : 0|32@1+ (1,0) [0|4294967295] ""  ECU
CM_ BO_ 189 "Navigation fault";
CM_ SG_ 189 NavigationFault "Navigation fault";

BO_ 230 Injection: 2 ECU
 SG_ Injection : 0|16@1+ (0.1,0) [0|65535] "mm3/st"  ECU
CM_ BO_ 230 "Injection";
CM_ SG_ 230 Injection "Injection in mm3/st";

BO_ 329 CrankcaseBlowby: 2 ECU
 SG_ CrankcaseBlowby : 0|16@1+ (0.05,0) [0|65535] "kPa"  ECU
CM_ BO_ 329 "Crankcase Blow-by";
CM_ SG_ 329 CrankcaseBlowby "Crankcase blow-by pressure in kPa";

BO_ 233 TurbochargerBoost: 2 ECU
 SG_ TurbochargerBoost : 0|16@1+ (0.2,0) [0|65535] "kPa"  ECU
CM_ BO_ 233 "Turbocharger Boost";
CM_ SG_ 233 TurbochargerBoost "Turbocharger boost in kPa";

BO_ 321 RailPressure: 2 ECU
 SG_ RailPressure : 0|16@1+ (0.003906,0) [0|65535] "MPa"  ECU
CM_ BO_ 321 "Rail Pressure";
CM_ SG_ 321 RailPressure "Rail pressure in MPa";

BO_ 324 EngineStatus: 1 ECU
 SG_ EngineStatus : 0|8@1+ (1,0) [0|255] ""  ECU
CM_ BO_ 324 "Engine Status";
CM_ SG_ 324 EngineStatus "Engine status";

BO_ 4051 Open: 1 ECU
 SG_ Open : 0|8@1+ (1,0) [0|1] ""  ECU
CM_ BO_ 4051 "Open";
CM_ SG_ 4051 Open "Open state";

BO_ 4052 Close: 1 ECU
 SG_ Close : 0|8@1+ (1,0) [0|1] ""  ECU
CM_ BO_ 4052 "Close";
CM_ SG_ 4052 Close "Close state";

BO_ 4053 Open: 1 ECU
 SG_ Open : 0|8@1+ (1,0) [0|1] ""  ECU
CM_ BO_ 4053 "Open";
CM_ SG_ 4053 Open "Open state";

BO_ 4054 Close: 1 ECU
 SG_ Close : 0|8@1+ (1,0) [0|1] ""  ECU
CM_ BO_ 4054 "Close";
CM_ SG_ 4054 Close "Close state";

BO_ 4055 Signal: 1 ECU
 SG_ Signal : 0|8@1+ (1,0) [0|1] ""  ECU
CM_ BO_ 4055 "Signal";
CM_ SG_ 4055 Signal "Signal state";

BO_ 4056 Switch: 1 ECU
 SG_ Switch : 0|8@1+ (1,0) [0|1] ""  ECU
CM_ BO_ 4056 "Switch";
CM_ SG_ 4056 Switch "Switch state";

BO_ 4057 Velocity: 2 ECU
 SG_ Velocity : 0|16@1+ (0.01,-300) [0|65535] "deg/sec"  ECU
CM_ BO_ 4057 "Velocity";
CM_ SG_ 4057 Velocity "Velocity in deg/sec";

BO_ 4058 Velocity: 2 ECU
 SG_ Velocity : 0|16@1+ (0.01,-300) [0|65535] "deg/sec"  ECU
CM_ BO_ 4058 "Velocity";
CM_ SG_ 4058 Velocity "Velocity in deg/sec";

BO_ 4059 Acceleration: 2 ECU
 SG_ Acceleration : 0|16@1+ (0.01,0) [0|65535] "m/s2"  ECU
CM_ BO_ 4059 "Acceleration";
CM_ SG_ 4059 Acceleration "Acceleration in m/s2";

BO_ 4060 Acceleration: 2 ECU
 SG_ Acceleration : 0|16@1+ (0.01,0) [0|65535] "m/s2"  ECU
CM_ BO_ 4060 "Acceleration";
CM_ SG_ 4060 Acceleration "Acceleration in m/s2";

BO_ 4061 VehiclePitchAngle: 2 ECU
 SG_ VehiclePitchAngle : 0|16@1+ (0.01,-50) [0|65535] "deg"  ECU
CM_ BO_ 4061 "Vehicle Pitch Angle";
CM_ SG_ 4061 VehiclePitchAngle "Vehicle pitch angle in deg";

BO_ 4062 VehicleRollAngle: 2 ECU
 SG_ VehicleRollAngle : 0|16@1+ (0.01,-50) [0|65535] "deg"  ECU
CM_ BO_ 4062 "Vehicle Roll Angle";
CM_ SG_ 4062 VehicleRollAngle "Vehicle roll angle in deg";

BO_ 4063 VehicleYawAngle: 2 ECU
 SG_ VehicleYawAngle : 0|16@1+ (0.01,-360) [0|65535] "deg"  ECU
CM_ BO_ 4063 "Vehicle Yaw Angle";
CM_ SG_ 4063 VehicleYawAngle "Vehicle yaw angle in deg"; 