VERSION ""
NS_ : 
    NS_DESC_
    CM_
    BA_DEF_
    BA_
    VAL_
    CAT_DEF_
    CAT_
    FILTER
    BA_DEF_DEF_
    EV_DATA_
    ENVVAR_DATA_
    SGTYPE_
    SGTYPE_VAL_
    BA_DEF_SGTYPE_
    BA_SGTYPE_
    SIG_TYPE_REF_
    VAL_TABLE_
    SIG_GROUP_
    SIG_VALTYPE_
    SIGTYPE_VALTYPE_
    BO_TX_BU_
    BA_DEF_REL_
    BA_REL_
    BA_DEF_DEF_REL_
    BU_SG_REL_
    BU_EV_REL_
    BU_BO_REL_
    SG_MUL_VAL_

BS_:
BU_: ECU

BO_ 101 TotalVehicleHours: 4 ECU
 SG_ TotalVehicleHours : 0|32@1+ (0.05,0) [0|4294967295] "h"  ECU
CM_ BO_ 101 "Total Vehicle Hours";
CM_ SG_ 101 TotalVehicleHours "Total hours the vehicle has been running";

BO_ 120 Calendar: 4 ECU
 SG_ Calendar : 0|32@1+ (1,0) [0|4294967295] "MonthDayYearHours"  ECU
CM_ BO_ 120 "Calendar";
CM_ SG_ 120 Calendar "Format: Month[1B] + Day[1B] + Year(Offset from 2000)[1B] + Hours[1B]";

BO_ 140 VehicleDistance: 4 ECU
 SG_ VehicleDistance : 0|32@1+ (1,0) [0|4294967295] "m"  ECU
CM_ BO_ 140 "Vehicle Distance";
CM_ SG_ 140 VehicleDistance "Total distance the vehicle has traveled in meters";

BO_ 150 Speed: 2 ECU
 SG_ Speed : 0|16@1+ (0.003906,0) [0|65535] "km/h"  ECU
CM_ BO_ 150 "Vehicle Speed";
CM_ SG_ 150 Speed "Vehicle speed in km/h";

BO_ 182 Temperature: 2 ECU
 SG_ Temperature : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 182 "Temperature";
CM_ SG_ 182 Temperature "Temperature in deg C";

BO_ 202 CrankcaseBlowby: 2 ECU
 SG_ CrankcaseBlowby : 0|16@1+ (0.05,0) [0|65535] "kPa"  ECU
CM_ BO_ 202 "Crankcase Blow-by";
CM_ SG_ 202 CrankcaseBlowby "Crankcase blow-by pressure in kPa";

BO_ 203 PedalPosition1: 1 ECU
 SG_ PedalPosition1 : 0|8@1+ (0.5,0) [0|255] "%"  ECU
CM_ BO_ 203 "Pedal Position 1";
CM_ SG_ 203 PedalPosition1 "Pedal position 1 in percent";

BO_ 209 Engine1Speed: 2 ECU
 SG_ Engine1Speed : 0|16@1+ (0.125,0) [0|65535] "rpm"  ECU
CM_ BO_ 209 "Engine 1 Speed";
CM_ SG_ 209 Engine1Speed "Engine 1 speed in rpm";

BO_ 211 Temperature1: 2 ECU
 SG_ Temperature1 : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 211 "Temperature 1";
CM_ SG_ 211 Temperature1 "Temperature 1 in deg C";

BO_ 213 Engine1OilPressure: 1 ECU
 SG_ Engine1OilPressure : 0|8@1+ (4,0) [0|255] "kPa"  ECU
CM_ BO_ 213 "Engine 1 Oil Pressure";
CM_ SG_ 213 Engine1OilPressure "Engine 1 oil pressure in kPa";

BO_ 222 Temperature: 1 ECU
 SG_ Temperature : 0|8@1+ (1,-40) [0|255] "deg C"  ECU
CM_ BO_ 222 "Temperature";
CM_ SG_ 222 Temperature "Temperature in deg C";

BO_ 230 Injection: 2 ECU
 SG_ Injection : 0|16@1+ (0.1,0) [0|65535] "mm3/st"  ECU
CM_ BO_ 230 "Injection";
CM_ SG_ 230 Injection "Injection in mm3/st";

BO_ 231 Engine1FuelRate: 2 ECU
 SG_ Engine1FuelRate : 0|16@1+ (0.05,0) [0|65535] "L/h"  ECU
CM_ BO_ 231 "Engine 1 Fuel Rate";
CM_ SG_ 231 Engine1FuelRate "Engine 1 fuel rate in L/h";

BO_ 233 TurbochargerBoost: 2 ECU
 SG_ TurbochargerBoost : 0|16@1+ (0.2,0) [0|65535] "kPa"  ECU
CM_ BO_ 233 "Turbocharger Boost";
CM_ SG_ 233 TurbochargerBoost "Turbocharger boost in kPa";

BO_ 246 Port1Temperature: 2 ECU
 SG_ Port1Temperature : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 246 "Port 1 Temperature";
CM_ SG_ 246 Port1Temperature "Port 1 temperature in deg C";

BO_ 247 Port2Temperature: 2 ECU
 SG_ Port2Temperature : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 247 "Port 2 Temperature";
CM_ SG_ 247 Port2Temperature "Port 2 temperature in deg C";

BO_ 248 Port3Temperature: 2 ECU
 SG_ Port3Temperature : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 248 "Port 3 Temperature";
CM_ SG_ 248 Port3Temperature "Port 3 temperature in deg C";

BO_ 249 Port4Temperature: 2 ECU
 SG_ Port4Temperature : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 249 "Port 4 Temperature";
CM_ SG_ 249 Port4Temperature "Port 4 temperature in deg C";

BO_ 607 LockupEngaged: 1 ECU
 SG_ LockupEngaged : 0|8@1+ (1,0) [0|1] "ONOFF"  ECU
CM_ BO_ 607 "Lockup Engaged";
CM_ SG_ 607 LockupEngaged "Lockup engaged ON/OFF";

BO_ 612 Temperature1: 2 ECU
 SG_ Temperature1 : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 612 "Temperature 1";
CM_ SG_ 612 Temperature1 "Temperature 1 in deg C";

BO_ 620 Range: 2 ECU
 SG_ Range : 0|16@1+ (1,0) [0|65535] "ASCII"  ECU
CM_ BO_ 620 "Range";
CM_ SG_ 620 Range "Range as ASCII";

BO_ 803 LeftFront: 2 ECU
 SG_ LeftFront : 0|16@1+ (0.01,0) [0|65535] "MPa"  ECU
CM_ BO_ 803 "Left Front";
CM_ SG_ 803 LeftFront "Left front pressure in MPa";

BO_ 804 RightRear: 2 ECU
 SG_ RightRear : 0|16@1+ (0.01,0) [0|65535] "MPa"  ECU
CM_ BO_ 804 "Right Rear";
CM_ SG_ 804 RightRear "Right rear pressure in MPa";

BO_ 807 FootBrakePosition: 1 ECU
 SG_ FootBrakePosition : 0|8@1+ (1,0) [0|255] "%"  ECU
CM_ BO_ 807 "Foot Brake Position";
CM_ SG_ 807 FootBrakePosition "Foot brake position in percent";

BO_ 808 TemperatureFront: 1 ECU
 SG_ TemperatureFront : 0|8@1+ (1,-40) [0|255] "deg C"  ECU
CM_ BO_ 808 "Temperature Front";
CM_ SG_ 808 TemperatureFront "Front temperature in deg C";

BO_ 809 TemperatureRear: 1 ECU
 SG_ TemperatureRear : 0|8@1+ (1,-40) [0|255] "deg C"  ECU
CM_ BO_ 809 "Temperature Rear";
CM_ SG_ 809 TemperatureRear "Rear temperature in deg C";

BO_ 813 RetarderPosition: 1 ECU
 SG_ RetarderPosition : 0|8@1+ (1,0) [0|255] "%"  ECU
CM_ BO_ 813 "Retarder Position";
CM_ SG_ 813 RetarderPosition "Retarder position in percent";

BO_ 2401 PitchInclinometer: 2 ECU X
 SG_ PitchInclinometer : 0|16@1+ (0.0078125,-200) [0|65535] "deg"  ECU
CM_ BO_ 2401 "Pitch/Inclinometer";
CM_ SG_ 2401 PitchInclinometer "Pitch/Inclinometer in deg";

BO_ 2404 BodySeating: 1 ECU
 SG_ BodySeating : 0|8@1+ (1,0) [0|255] "-"  ECU
CM_ BO_ 2404 "Body Seating";
CM_ SG_ 2404 BodySeating "Body seating";

BO_ 2627 LiveWeight: 2 ECU
 SG_ LiveWeight : 0|16@1+ (0.1,0) [0|65535] "ton"  ECU
CM_ BO_ 2627 "Live Weight";
CM_ SG_ 2627 LiveWeight "Live weight in tons"; 