#!/usr/bin/env python3
import os
import cantools
import can
import time
from datetime import datetime
import json
from typing import Dict, Any
import csv

class CANReceiver:
    def __init__(self):
        self.dbc_path = os.path.join(os.path.dirname(__file__), 'dbc')
        self.db = cantools.database.Database()
        self.load_all_dbc_files()
        self.bus = None
        self.message_history: Dict[int, Dict[str, Any]] = {}
        
    def load_all_dbc_files(self):
        """Load all DBC files from the dbc directory"""
        try:
            dbc_files = [
                'dbc/15.dbc',
                'dbc/30.dbc',
                'dbc/78.dbc',
                'dbc/201.dbc',
                'dbc/208.dbc',
                'dbc/301.dbc'
            ]
            
            for dbc_file in dbc_files:
                try:
                    dbc_path = os.path.join(os.path.dirname(__file__), dbc_file)
                    print(f"Loading DBC file: {dbc_file}")
                    
                    # Read the file content first
                    with open(dbc_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Add the DBC content to the database
                    self.db.add_dbc_string(content)
                    print(f"Successfully loaded {dbc_file}")
                    
                except Exception as e:
                    print(f"Warning: Could not load {dbc_file}: {e}")
                    continue
            
            print(f"Successfully loaded {len(dbc_files)} DBC files")
            
        except Exception as e:
            print(f"Error loading DBC files: {e}")
            raise

    def format_value(self, value: Any, unit: str = "") -> str:
        """Format value with appropriate unit"""
        if isinstance(value, (int, float)):
            return f"{value:.2f}{unit}"
        return str(value)

    def decode_message(self, msg) -> Dict[str, Any]:
        """Decode CAN message and return formatted data"""
        try:
            message = self.db.get_message_by_frame_id(msg.arbitration_id)
            if not message:
                return None

            decoded_msg = self.db.decode_message(msg.arbitration_id, msg.data)
            timestamp = datetime.fromtimestamp(msg.timestamp).strftime('%Y-%m-%d %H:%M:%S.%f')

            # Get signal descriptions and units
            signal_info = {}
            for signal in message.signals:
                value = decoded_msg.get(signal.name)
                unit = signal.unit if signal.unit else ""
                
                # Handle enumerated values if they exist
                if hasattr(signal, 'choices') and signal.choices:
                    value = signal.choices.get(value, value)
                
                signal_info[signal.name] = {
                    'value': value,
                    'unit': unit,
                    'description': signal.comment if signal.comment else signal.name
                }

            return {
                'timestamp': timestamp,
                'message_name': message.name,
                'message_id': f"0x{msg.arbitration_id:X}",
                'signals': signal_info
            }

        except Exception as e:
            print(f"Error decoding message ID 0x{msg.arbitration_id:X}: {e}")
            return None

    def print_message(self, decoded_data: Dict[str, Any]):
        """Print decoded message in a formatted way"""
        if not decoded_data:
            return

        print("\n" + "="*80)
        print(f"Timestamp: {decoded_data['timestamp']}")
        print(f"Message: {decoded_data['message_name']}")
        print(f"ID: {decoded_data['message_id']}")
        print("\nSignals:")
        
        for signal_name, signal_data in decoded_data['signals'].items():
            value = signal_data['value']
            unit = signal_data['unit']
            description = signal_data['description']
            
            formatted_value = self.format_value(value, unit)
            print(f"  {signal_name}:")
            print(f"    Value: {formatted_value}")
            print(f"    Description: {description}")
        
        print("="*80)

    def save_to_json(self, decoded_data: Dict[str, Any], filename: str = "can_data.json"):
        """Save decoded data to JSON file"""
        try:
            # Update message history
            msg_id = decoded_data['message_id']
            self.message_history[msg_id] = decoded_data

            # Save to file
            with open(filename, 'w') as f:
                json.dump(self.message_history, f, indent=2)
        except Exception as e:
            print(f"Error saving to JSON: {e}")

    def log_to_csv(self, decoded_data, filename="can_data.csv"):
        try:
            file_exists = os.path.isfile(filename)
            with open(filename, 'a', newline='') as csvfile:
                writer = csv.writer(csvfile)
                if not file_exists:
                    # Write header
                    header = ["timestamp", "message_name", "message_id"] + list(decoded_data['signals'].keys())
                    writer.writerow(header)
                row = [
                    decoded_data['timestamp'],
                    decoded_data['message_name'],
                    decoded_data['message_id']
                ] + [decoded_data['signals'][sig]['value'] for sig in decoded_data['signals']]
                writer.writerow(row)
        except Exception as e:
            print(f"Error logging to CSV: {e}")

    def process_can_messages(self):
        """Main processing loop for CAN messages"""
        try:
            print("Connecting to CAN bus...")
            # Try different CAN interfaces for Windows
            try:
                # Try Kvaser first
                self.bus = can.interface.Bus(channel=0, interface='kvaser')
            except Exception as e1:
                try:
                    # Try PCAN if Kvaser fails
                    self.bus = can.interface.Bus(channel='PCAN_USBBUS1', interface='pcan')
                except Exception as e2:
                    try:
                        # Try USB-to-CAN adapter as last resort
                        self.bus = can.interface.Bus(channel='COM3', interface='slcan')
                    except Exception as e3:
                        print("Could not connect to any CAN interface")
                        print("Kvaser error:", e1)
                        print("PCAN error:", e2)
                        print("USB-to-CAN error:", e3)
                        raise

            print("Connected successfully")
            
            print("\nStarting CAN message processing...")
            print("Press Ctrl+C to stop")
            
            while True:
                try:
                    msg = self.bus.recv(timeout=1.0)
                    if msg:
                        decoded_data = self.decode_message(msg)
                        if decoded_data:
                            self.print_message(decoded_data)
                            self.log_to_csv(decoded_data)
                        
                except Exception as e:
                    print(f"Error reading message: {e}")
                    time.sleep(1)  # Wait before retrying
                    
        except KeyboardInterrupt:
            print("\nStopping CAN message processing...")
        except Exception as e:
            print(f"Error in main processing loop: {e}")
        finally:
            if self.bus:
                self.bus.shutdown()
                print("Disconnected from CAN bus")

def main():
    try:
        receiver = CANReceiver()
        receiver.process_can_messages()
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1
    return 0

if __name__ == "__main__":
    exit(main())