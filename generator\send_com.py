import can
import csv
import time
import platform
import sys

def hex_to_int(hex_str):
    """Convert hex string to integer, handle empty strings."""
    if not hex_str or hex_str.strip() == '':
        return 0
    return int(hex_str, 16)

def read_can_data(csv_file):
    with open(csv_file, 'r', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            can_id = hex_to_int(row['id'])
            dlc = int(row['DLC'])
            data = []
            for i in range(dlc):
                data_byte = row.get(f'Data{i}', '')
                data.append(hex_to_int(data_byte))
            timestamp = float(row['Time'])
            yield {
                'timestamp': timestamp,
                'id': can_id,
                'dlc': dlc,
                'data': data
            }

def main():
    # Determine the appropriate interface based on the operating system
    system = platform.system().lower()
    
    try:
        if system == 'windows':
            # For Windows, try slcan first (common for USB-to-CAN adapters)
            try:
                bus = can.interface.Bus(channel='COM1', interface='slcan', bitrate=250000)
                print("Using slcan interface on COM1")
            except Exception as e:
                print("Failed to initialize slcan interface:", e)
                print("\nPlease ensure you have:")
                print("1. A USB-to-CAN adapter connected")
                print("2. The correct COM port (check Device Manager)")
                print("3. The appropriate drivers installed")
                sys.exit(1)
        elif system == 'linux':
            # For Linux, use socketcan
            try:
                bus = can.interface.Bus(channel='can0', interface='socketcan', bitrate=250000)
                print("Using socketcan interface on can0")
            except Exception as e:
                print("Failed to initialize socketcan interface:", e)
                print("\nPlease ensure you have:")
                print("1. A CAN interface connected")
                print("2. SocketCAN drivers loaded (modprobe can, modprobe can_raw)")
                print("3. Interface configured (ip link set can0 type can bitrate 250000)")
                sys.exit(1)
        else:
            print(f"Unsupported operating system: {system}")
            sys.exit(1)

        MAX_RETRIES = 5
        BASE_DELAY = 0.01  # 10ms base delay between messages
        INITIAL_RETRY_DELAY = 0.05  # 50ms initial retry delay

        try:
            last_timestamp = None
            for can_data in read_can_data('sample/raw_7.csv'):
                if last_timestamp is not None:
                    delay = can_data['timestamp'] - last_timestamp
                    if delay > 0:
                        time.sleep(delay)
                    else:
                        time.sleep(BASE_DELAY)
                
                msg = can.Message(
                    arbitration_id=can_data['id'],
                    data=can_data['data'],
                    is_extended_id=True
                )
                
                retry_delay = INITIAL_RETRY_DELAY
                for attempt in range(MAX_RETRIES):
                    try:
                        bus.send(msg)
                        print(f"Sent: ID=0x{can_data['id']:X}, Data={[hex(x) for x in can_data['data']]}")
                        break
                    except can.CanError as e:
                        if "buffer overflow" in str(e).lower():
                            if attempt < MAX_RETRIES - 1:
                                print(f"Buffer overflow, retrying in {retry_delay:.3f}s... (Attempt {attempt + 1}/{MAX_RETRIES})")
                                time.sleep(retry_delay)
                                retry_delay *= 2
                            else:
                                print(f"Failed to send message after {MAX_RETRIES} attempts: {e}")
                                time.sleep(0.1)
                        else:
                            raise
                
                last_timestamp = can_data['timestamp']

        except can.CanError as e:
            print(f"CAN Error: {e}")
        finally:
            bus.shutdown()

    except Exception as e:
        print(f"Error initializing CAN interface: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
