VERSION ""
NS_ : 
    NS_DESC_
    CM_
    BA_DEF_
    BA_
    VAL_
    CAT_DEF_
    CAT_
    FILTER
    BA_DEF_DEF_
    EV_DATA_
    ENVVAR_DATA_
    SGTYPE_
    SGTYPE_VAL_
    BA_DEF_SGTYPE_
    BA_SGTYPE_
    SIG_TYPE_REF_
    VAL_TABLE_
    SIG_GROUP_
    SIG_VALTYPE_
    SIGTYPE_VALTYPE_
    BO_TX_BU_
    BA_DEF_REL_
    BA_REL_
    BA_DEF_DEF_REL_
    BU_SG_REL_
    BU_EV_REL_
    BU_BO_REL_
    SG_MUL_VAL_

BS_:
BU_: ECU

BO_ 101 TotalVehicleHours: 4 ECU
 SG_ TotalVehicleHours : 0|32@1+ (0.05,0) [0|4294967295] "h"  ECU
CM_ BO_ 101 "Total Vehicle Hours";
CM_ SG_ 101 TotalVehicleHours "Total hours the vehicle has been running";

BO_ 182 Temperature: 2 ECU
 SG_ Temperature : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 182 "Temperature";
CM_ SG_ 182 Temperature "Temperature in deg C";

BO_ 209 Engine1Speed: 2 ECU
 SG_ Engine1Speed : 0|16@1+ (0.125,0) [0|65535] "rpm"  ECU
CM_ BO_ 209 "Engine 1 Speed";
CM_ SG_ 209 Engine1Speed "Engine 1 speed in rpm";

BO_ 211 Temperature1: 2 ECU
 SG_ Temperature1 : 0|16@1+ (0.03125,-273) [0|65535] "deg C"  ECU
CM_ BO_ 211 "Temperature 1";
CM_ SG_ 211 Temperature1 "Temperature 1 in deg C";

BO_ 213 Engine1OilPressure: 1 ECU
 SG_ Engine1OilPressure : 0|8@1+ (4,0) [0|255] "kPa"  ECU
CM_ BO_ 213 "Engine 1 Oil Pressure";
CM_ SG_ 213 Engine1OilPressure "Engine 1 oil pressure in kPa";

BO_ 222 Temperature: 1 ECU
 SG_ Temperature : 0|8@1+ (1,-40) [0|255] "deg C"  ECU
CM_ BO_ 222 "Temperature";
CM_ SG_ 222 Temperature "Temperature in deg C";

BO_ 320 Pressure: 2 ECU
 SG_ Pressure : 0|16@1+ (0.03125,-1000) [0|65535] "kPa"  ECU
CM_ BO_ 320 "Pressure";
CM_ SG_ 320 Pressure "Pressure in kPa";

BO_ 1001 Front1MainLoader: 2 ECU
 SG_ Front1MainLoader : 0|16@1+ (0.003906,0) [0|65535] "MPa"  ECU
CM_ BO_ 1001 "1/Front 1/Main/Loader";
CM_ SG_ 1001 Front1MainLoader "Front 1/Main/Loader pressure in MPa";

BO_ 1155 Temperature: 1 ECU
 SG_ Temperature : 0|8@1+ (1,-40) [0|255] "deg C"  ECU
CM_ BO_ 1155 "Temperature";
CM_ SG_ 1155 Temperature "Temperature in deg C";

BO_ 1201 Rear2: 2 ECU
 SG_ Rear2 : 0|16@1+ (0.003906,0) [0|65535] "MPa"  ECU
CM_ BO_ 1201 "2/Rear 1";
CM_ SG_ 1201 Rear2 "Rear 1 pressure in MPa";

BO_ 1401 Front2: 2 ECU
 SG_ Front2 : 0|16@1+ (0.003906,0) [0|65535] "MPa"  ECU
CM_ BO_ 1401 "3/Front 2";
CM_ SG_ 1401 Front2 "Front 2 pressure in MPa"; 