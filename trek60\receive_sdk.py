#!/usr/bin/env python3
import os
import sys
import cantools
import time
from datetime import datetime
import json
import ctypes
import csv

# Define the CAN message struct for vcil_can_read
class VcilCanMessage(ctypes.Structure):
    _fields_ = [
        ("port", ctypes.c_ubyte),
        ("length", ctypes.c_char),
        ("remote_request", ctypes.c_bool),
        ("extended_frame", ctypes.c_bool),
        ("id", ctypes.c_uint32),
        ("data", ctypes.c_ubyte * 8)
    ]

class CANReceiverSDK:
    def __init__(self):
        self.db = cantools.database.Database()
        self.load_all_dbc_files()
        self.dll = None
        self.message_history = {}

    def load_all_dbc_files(self):
        dbc_files = [
            'dbc/15.dbc',
            'dbc/30.dbc',
            'dbc/78.dbc',
            'dbc/201.dbc',
            'dbc/208.dbc',
            'dbc/301.dbc'
        ]
        for dbc_file in dbc_files:
            try:
                dbc_path = os.path.join(os.path.dirname(__file__), dbc_file)
                print(f"Loading DBC file: {dbc_file}")
                with open(dbc_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.db.add_dbc_string(content)
                print(f"Successfully loaded {dbc_file}")
            except Exception as e:
                print(f"Warning: Could not load {dbc_file}: {e}")

    def decode_message(self, can_id, data_bytes):
        try:
            message = self.db.get_message_by_frame_id(can_id)
            if not message:
                return None
            decoded_msg = self.db.decode_message(can_id, data_bytes)
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
            signal_info = {}
            for signal in message.signals:
                value = decoded_msg.get(signal.name)
                unit = signal.unit if signal.unit else ""
                if hasattr(signal, 'choices') and signal.choices:
                    value = signal.choices.get(value, value)
                signal_info[signal.name] = {
                    'value': value,
                    'unit': unit,
                    'description': signal.comment if signal.comment else signal.name
                }
            return {
                'timestamp': timestamp,
                'message_name': message.name,
                'message_id': f"0x{can_id:X}",
                'signals': signal_info
            }
        except Exception as e:
            print(f"Error decoding message ID 0x{can_id:X}: {e}")
            return None

    def print_message(self, decoded_data):
        if not decoded_data:
            return
        print("\n" + "="*80)
        print(f"Timestamp: {decoded_data['timestamp']}")
        print(f"Message: {decoded_data['message_name']}")
        print(f"ID: {decoded_data['message_id']}")
        print("\nSignals:")
        for signal_name, signal_data in decoded_data['signals'].items():
            value = signal_data['value']
            unit = signal_data['unit']
            description = signal_data['description']
            print(f"  {signal_name}: {value} {unit} ({description})")
        print("="*80)

    def save_to_json(self, decoded_data, filename="can_data.json"):
        try:
            msg_id = decoded_data['message_id']
            self.message_history[msg_id] = decoded_data
            with open(filename, 'w') as f:
                json.dump(self.message_history, f, indent=2)
        except Exception as e:
            print(f"Error saving to JSON: {e}")

    def log_to_csv(self, decoded_data, filename="can_data.csv"):
        try:
            file_exists = os.path.isfile(filename)
            with open(filename, 'a', newline='') as csvfile:
                writer = csv.writer(csvfile)
                if not file_exists:
                    # Write header
                    header = ["timestamp", "message_name", "message_id"] + list(decoded_data['signals'].keys())
                    writer.writerow(header)
                row = [
                    decoded_data['timestamp'],
                    decoded_data['message_name'],
                    decoded_data['message_id']
                ] + [decoded_data['signals'][sig]['value'] for sig in decoded_data['signals']]
                writer.writerow(row)
        except Exception as e:
            print(f"Error logging to CSV: {e}")

    def load_sdk_library(self):
        dll_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'sdk', 'bin'))
        os.environ['PATH'] = dll_dir + os.pathsep + os.environ.get('PATH', '')
        if sys.platform.startswith('win'):
            print("Loading SDK library: vcil.dll")
            dll_path = os.path.join(dll_dir, 'vcil.dll')
        elif sys.platform.startswith('linux'):
            dll_path = os.path.join(dll_dir, 'libvcil.so')
        else:
            raise RuntimeError(f"Unsupported platform: {sys.platform}")
        print(f"Loading SDK library: {dll_path}")
        if not os.path.exists(dll_path):
            raise FileNotFoundError(f"SDK library not found: {dll_path}")
        return ctypes.CDLL(dll_path)

    def process_can_messages(self):
        try:
            self.dll = self.load_sdk_library()
            # Set return type for vcil_can_read
            self.dll.vcil_can_read.restype = ctypes.c_int

            print("Connected successfully")
            print("\nStarting CAN message processing...")
            print("Press Ctrl+C to stop")

            msg = VcilCanMessage()
            while True:
                try:
                    result = self.dll.vcil_can_read(ctypes.byref(msg))
                    if result == 0:  # Success
                        data_bytes = bytes(msg.data[:msg.length])
                        print(f"ID: {msg.id:X}, Data: {[msg.data[i] for i in range(msg.length)]}")
                        decoded = self.decode_message(msg.id, data_bytes)
                        if decoded:
                            self.print_message(decoded)
                            self.save_to_json(decoded)
                            self.log_to_csv(decoded)
                    else:
                        time.sleep(0.01)  # Avoid busy loop if no message
                except Exception as e:
                    print(f"Error reading message: {e}")
                    time.sleep(1)
        except KeyboardInterrupt:
            print("\nStopping CAN message processing...")
        except Exception as e:
            print(f"Error in main processing loop: {e}")

def main():
    try:
        receiver = CANReceiverSDK()
        receiver.process_can_messages()
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1
    return 0

if __name__ == "__main__":
    exit(main())