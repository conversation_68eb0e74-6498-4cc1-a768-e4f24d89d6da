### CAN UTILS

## Environtment
- Download and install python 3.11
- Download and install <PERSON><PERSON><PERSON> in https://kvaser.com/download/
- Run the below command in terminal
`pip install -r requirements.txt`

## Create CAN frames using sample data(raw_7.csv)
- Run the below script on window PC
`python ./generator/send.py`

## Receive CAN frames and process them on Trek60
- Run below script using MRM CVIL SDK
`python ./trek60/recive_sdk.py`

- Run below script using python can
`python ./trek60/recive.py`

